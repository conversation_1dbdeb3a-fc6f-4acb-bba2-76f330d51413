// app.js

import express from 'express';

const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Sample route setup (replace with your actual routes)
// handle auth and unauth routes
app.use('/auth/api', require('./routes/authRoutes'));
app.use('/api', require('./routes/unauthRoutes'));

// Base health check route
app.get('/', (req, res) => {
    res.json({ message: 'Welcome to the Social Media API!' });
});

// handle global errors
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('Something broke!');
});


module.exports = app;
